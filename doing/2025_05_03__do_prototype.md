# Web Interior Designer – DigitalOcean Deployment Plan
_Based on the original AWS-centric spec_ :contentReference[oaicite:2]{index=2}

## 0  Overview
The stack is now **100 % DigitalOcean**:

| Layer | Service |
|-------|---------|
| **Frontend** | **App Platform — Static Site** (builds React from Git) |
| **Backend API** | **App Platform — Docker Image** (FastAPI) |
| **Object Storage** | **Spaces** (S3-compatible) |
| **Relational DB** | **Managed PostgreSQL** |
| **Auth** | FastAPI Users + JWT (access + refresh) |
| **LLM Image Edit** | OpenAI Images API |

All pieces live in a single DO project so your billing dashboard shows the _one_ number that matters.

---

## 1  Architecture Diagram

```mermaid
graph TD
    subgraph Browser
        A[React SPA]
    end

    subgraph DO-AppPlatform
        B[Static Site<br>(/dist)]
        C[FastAPI Container]
    end

    subgraph DigitalOcean
        D[Spaces<br>object storage]
        E[Managed PostgreSQL]
    end

    A -->|1. POST /api/jobs<br>multipart image+style| C
    C -->|2. PUT original.jpg| D
    C -->|3. Call OpenAI| F(OpenAI)
    F -->|4. redesign bytes| C
    C -->|5. PUT redesign.png| D
    C -->|6. INSERT row| E
    C -->|7. Return JSON {url...}| A
    A -->|8. GET signed URL| D
```

---

## 2  Detailed Task List

### 2.1  Repository layout
```
/frontend      React + Vite
/backend       FastAPI app
/prompt-bank   *.txt
Dockerfile
app.yaml       # App Platform spec
```

### 2.2  Frontend (React + Vite)

- [x] `UploadPhoto` → accepts image file.
- [x] `SelectStyle` → grid of cards.
- [x] `ResultCompare` → side-by-side.
- [ ] **Auth wrapper** – checks JWT in `localStorage`; redirects to `/login` if absent.
- [ ] `/login` & `/signup` forms (email + password).

Deploy:
```yaml
# app.yaml snippet
name: interior-frontend
services:
  - run_command: npm run build
    static_sites:
      - name: react-static
        github:
          branch: main
          deploy_on_push: true
          repo: your/repo
```

### 2.3  Backend (FastAPI)

#### 2.3.1  Core API routes
| Method | Path | Auth? | Purpose |
|--------|------|-------|---------|
| POST | `/auth/jwt/login` | – | Obtain tokens (`fastapi-users`) |
| POST | `/auth/jwt/refresh` | – | Rotate access token |
| POST | `/api/jobs` | ✔ | Upload image + style, start job |
| GET | `/api/jobs/{job_id}` | ✔ | Poll job status & URLs |
| GET | `/api/me` | ✔ | Return profile |

#### 2.3.2  Database schema (Alembic)

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  hashed_password TEXT NOT NULL,
  registered_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE jobs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_key TEXT NOT NULL,
  redesign_key TEXT,
  style TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  status TEXT DEFAULT 'processing',  -- processing | done | error
  error TEXT                         -- error message if status is 'error'
);

-- Add index for faster job lookups by user
CREATE INDEX idx_jobs_user_id ON jobs(user_id);
```

#### 2.3.3  `Dockerfile`

```dockerfile
FROM python:3.12-slim

ENV PYTHONUNBUFFERED=1
WORKDIR /app
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
```

#### 2.3.4  Upload + OpenAI logic with Background Processing

```python
import boto3, uuid, base64, os, httpx, sqlalchemy as sa
from fastapi import Depends, FastAPI, File, UploadFile, Form
from fastapi_users import FastAPIUsers, schemas
from openai import OpenAI
from rq import Queue
from redis import Redis

SPACES_KEY    = os.getenv("SPACES_KEY")
SPACES_SECRET = os.getenv("SPACES_SECRET")
ENDPOINT      = "https://nyc3.digitaloceanspaces.com"
BUCKET        = "interior-images"
REDIS_URL     = os.getenv("REDIS_URL", "redis://localhost:6379")

# Initialize S3 client for Spaces
s3 = boto3.client("s3",
                  endpoint_url=ENDPOINT,
                  aws_access_key_id=SPACES_KEY,
                  aws_secret_access_key=SPACES_SECRET)

# Initialize OpenAI client
openai = OpenAI(api_key=os.environ["OPENAI_API_KEY"])

# Initialize Redis and RQ
redis_conn = Redis.from_url(REDIS_URL)
job_queue = Queue('interior_designs', connection=redis_conn)

app = FastAPI()

# Background job function
def process_interior_design(job_id, user_id, original_key, style, prompt):
    """Process interior design with OpenAI in background"""
    try:
        # Get the original image from Spaces
        response = s3.get_object(Bucket=BUCKET, Key=original_key)
        body = response['Body'].read()

        # Call OpenAI API
        resp = openai.images.edit(
            model="gpt-image-1",
            image={"bytes": body},
            prompt=prompt,
            n=1,
            size="1024x1024",
            response_format="b64_json",
        )

        # Save the redesigned image
        redesign_bytes = base64.b64decode(resp.data[0].b64_json)
        redesign_key = f"redesign/{job_id}.png"
        s3.put_object(
            Bucket=BUCKET,
            Key=redesign_key,
            Body=redesign_bytes,
            ContentType="image/png"
        )

        # Update job status in database
        with db_session() as db:
            db.execute(sa.text(
                "UPDATE jobs SET status = 'done', redesign_key = :r WHERE id = :id"
            ), dict(id=job_id, r=redesign_key))

        return redesign_key
    except Exception as e:
        # Update job status to error
        with db_session() as db:
            db.execute(sa.text(
                "UPDATE jobs SET status = 'error', error = :err WHERE id = :id"
            ), dict(id=job_id, err=str(e)))
        raise

@app.post("/api/jobs", response_model=JobOut)
async def create_job(
    style: str = Form(...),
    image: UploadFile = File(...),
    user: User = Depends(current_active_user),
):
    # Generate job ID and save original image
    job_id = uuid.uuid4()
    key_orig = f"original/{job_id}.jpg"
    body = await image.read()
    s3.put_object(Bucket=BUCKET, Key=key_orig, Body=body, ContentType="image/jpeg")

    # Create job record in pending state
    with db_session() as db:
        db.execute(sa.text(
          "INSERT INTO jobs (id,user_id,original_key,style,status)"
          " VALUES (:id,:uid,:o,:s,'processing')"),
          dict(id=job_id, uid=user.id, o=key_orig, s=style))

    # Build prompt and queue background job
    prompt = build_prompt(style)
    job_queue.enqueue(
        process_interior_design,
        args=(job_id, user.id, key_orig, style, prompt),
        job_timeout='10m',  # OpenAI can take time
        result_ttl=86400,   # Store result for 1 day
    )

    # Return job ID immediately
    return JobOut(id=str(job_id), status="processing")

@app.get("/api/jobs/{job_id}", response_model=JobStatusOut)
async def get_job_status(
    job_id: str,
    user: User = Depends(current_active_user),
):
    # Check job status in database
    with db_session() as db:
        result = db.execute(sa.text(
            "SELECT status, redesign_key, error FROM jobs WHERE id = :id AND user_id = :uid"
        ), dict(id=job_id, uid=user.id)).fetchone()

    if not result:
        raise HTTPException(status_code=404, detail="Job not found")

    status, redesign_key, error = result

    if status == 'done' and redesign_key:
        # Generate presigned URL for completed job
        url = s3.generate_presigned_url(
            "get_object",
            Params={"Bucket": BUCKET, "Key": redesign_key},
            ExpiresIn=3600*24
        )
        return JobStatusOut(id=job_id, status=status, url=url)
    elif status == 'error':
        return JobStatusOut(id=job_id, status=status, error=error)
    else:
        return JobStatusOut(id=job_id, status=status)
```

#### 2.3.5  Worker Process Configuration

```python
# worker.py - Run as a separate process in the same container
import os
from rq import Worker, Queue, Connection
from redis import Redis

# Connect to Redis
redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
redis_conn = Redis.from_url(redis_url)

# Import job processing functions to make them available to the worker
from main import process_interior_design

if __name__ == '__main__':
    # Start worker with conservative settings for small instance
    with Connection(redis_conn):
        worker = Worker(
            ['interior_designs'],
            name='openai_worker',
            default_result_ttl=86400,
            job_monitoring_interval=30,
        )
        worker.work(with_scheduler=True)
```

#### 2.3.6  Dockerfile Update for Worker Process

```dockerfile
FROM python:3.12-slim

ENV PYTHONUNBUFFERED=1
WORKDIR /app
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ .

# Use supervisord to manage both API and worker processes
RUN pip install supervisor
COPY supervisord.conf .

# Start both processes using supervisord
CMD ["supervisord", "-c", "supervisord.conf"]
```

#### 2.3.7  Supervisor Configuration

```ini
# supervisord.conf
[supervisord]
nodaemon=true
user=root
logfile=/dev/stdout
logfile_maxbytes=0

[program:api]
command=uvicorn main:app --host 0.0.0.0 --port 8080
directory=/app
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
startretries=5

[program:worker]
command=python worker.py
directory=/app
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
startretries=5
```

### 2.4  Deployment Steps

#### 2.4.1  Deployment Troubleshooting

When deploying to DigitalOcean App Platform, be aware of these common issues:

- **Container Exit Codes**: If your container exits with a non-zero exit code, check that:
  - All required environment variables are configured
  - External services (database, Redis, etc.) are accessible
  - The code doesn't depend on resources that aren't available

- **Simplified Deployment Strategy**:
  - Start with a minimal "Hello World" version that has no external dependencies
  - Gradually add features and dependencies one by one
  - Test each step before moving to the next

- **Resource Constraints**:
  - The $5/mo instance (512 MB RAM) requires careful resource management
  - Use lightweight alternatives where possible (RQ instead of Celery, etc.)
  - Consider running both API and worker in the same container with supervisor

#### 2.4.2  CLI Deployment Steps

1. **Container Registry**
   ```bash
   doctl registry login
   docker build -t registry.digitalocean.com/$PROJECT/api:$(git rev-parse --short HEAD) .
   docker push registry.digitalocean.com/$PROJECT/api:$(git rev-parse --short HEAD)
   ```
2. **Database**
   ```
   doctl databases create interior-db --engine pg --size db-s-dev-database --region nyc3
   doctl databases connection interior-db  # grab URI for INFRA_VAR
   ```
3. **Spaces bucket**
   ```
   doctl spaces create interior-images --region nyc3
   doctl spaces cdn enable interior-images
   ```
4. **Secrets & env**
   ```
   doctl apps create --spec app.yaml \
     --env OPENAI_API_KEY=$OPENAI_KEY \
     --env DATABASE_URL=... \
     --env REDIS_URL=... \
     --secret SPACES_KEY=... \
     --secret SPACES_SECRET=...
   ```
5. **DNS & HTTPS** – point `app.example.com` at generated CNAME; HTTPS auto-provisions.

---

## 3  Authentication Flow

- **Registration** → `/auth/jwt/register` (FastAPI Users).
- Hash algorithm: `argon2id`.
- Access token TTL = 15 min; Refresh token TTL = 7 days.
- React stores the _access_ token in memory and the _refresh_ token in an HttpOnly cookie.

```
Browser ─► /auth/jwt/login (email, pwd)
          ◄─ tokens
Browser ─► /api/jobs     (Authorization: Bearer <access>)
If 401   ─► /auth/jwt/refresh
```

---

## 4  Cost Cheat-Sheet (May 2025, nyc3)

| Item | Qty | Price | Notes |
|------|-----|-------|-------|
| App Platform Basic, 1 container, 512 MiB | 1 | \$5 | CPU 0.5 vCPU |
| Spaces 250 GiB + 1 TiB egress | 1 | \$5 | Bundle |
| Managed PG dev-tier | 1 | \$15 | 1 vCPU / 1 GiB |
| **Subtotal ≈ \$25 / mo** | | | Similar to the AWS numbers but simpler billing. |

Billing alert e-mails at \$50, \$100, \$200 → `doctl billing alert create …`.

---

## 5  Next Milestones

0. **[COMPLETED] Deploy "Hello World" to DigitalOcean** - Created and tested a minimal version to verify deployment pipeline works correctly before implementing the full application. Created backend/hello_world.py, simplified main.py, frontend Hello World components, and deployment configuration in app.hello.yaml. Successfully deployed to DigitalOcean App Platform on a $5/mo instance.

1. **[COMPLETED] Frontend Static Site Deployment** - Successfully deployed React frontend as static site alongside backend API. Fixed multiple deployment issues including buildpack detection, TypeScript compilation errors, and service vs static site configuration. Both backend (at /api) and frontend (at /) now deploy successfully using minimal-spec.yaml.

2. **[COMPLETED] Fix Frontend Routing Issue** - The static site is deployed but showing "The requested page was not found" instead of Hello World component. Need to fix routing configuration so hello.html is served at root path instead of index.html. This is likely a simple index file configuration issue.

3. **[TODO] Remove Hello World Code** - Once full app is ready, clean up and remove:
   - backend/hello_world.py
   - frontend/src/HelloWorld.tsx
   - frontend/hello.html
   - build:hello npm script
   - vite.hello.config.js
   - app.hello.yaml (keep minimal-spec.yaml as it works)

4. **Implement Background Job Processing** - Add a lightweight job queue (RQ) to handle OpenAI API calls asynchronously, keeping the API responsive even on the smallest $5/mo instance size. This will allow handling more concurrent users and provide better UX with job status updates.

5. **Move Frontend upload to presigned PUT → Spaces** (save backend RAM).

6. **WebSockets or SSE** for real-time progress bar.

7. **Lifecycle worker** to downgrade objects older than 30 days to Backblaze B2 via rclone if cost grows.

8. **Cloudflare** in front if you need WAF rules or advanced DDoS shield.

---

### You can now copy-paste this file as your canonical **DigitalOcean deployment plan** and prompt your local LLM:

> _“Follow the plan section **2 Detailed Task List** step-by-step and stop after each numbered sub-task to let me confirm.”_

